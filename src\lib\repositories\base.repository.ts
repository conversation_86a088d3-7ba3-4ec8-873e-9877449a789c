import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  onSnapshot,
  serverTimestamp,
  query,
  orderBy,
  type DocumentData,
  type QueryConstraint,
  type Unsubscribe,
  type Timestamp
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import type {
  BaseEntity,
  CreateEntityData,
  UpdateEntityData,
  RepositoryResult,
  RepositoryListResult,
  SubscriptionCallback,
  SubscriptionErrorCallback,
  FirestoreTimestamp
} from '@/types/common'

/**
 * Base repository class that provides common CRUD operations for Firestore collections
 * with real-time subscriptions and proper TypeScript typing
 */
export abstract class BaseRepository<T extends BaseEntity> {
  protected collectionName: string

  constructor(collectionName: string) {
    this.collectionName = collectionName
  }

  /**
   * Convert Firestore timestamp to JavaScript Date
   */
  protected convertTimestamp(timestamp: Timestamp | FirestoreTimestamp | Date): Date {
    if (timestamp instanceof Date) {
      return timestamp
    }
    if ('seconds' in timestamp) {
      return new Date(timestamp.seconds * 1000)
    }
    return timestamp.toDate()
  }

  /**
   * Convert Firestore document data to typed entity
   */
  protected convertDocumentData(id: string, data: DocumentData): T {
    return {
      id,
      ...data,
      createdAt: data.createdAt ? this.convertTimestamp(data.createdAt) : new Date(),
      updatedAt: data.updatedAt ? this.convertTimestamp(data.updatedAt) : new Date(),
    } as T
  }

  /**
   * Subscribe to all documents in the collection with real-time updates
   */
  subscribeToAll(
    onData: SubscriptionCallback<T>,
    onError: SubscriptionErrorCallback,
    constraints: QueryConstraint[] = []
  ): Unsubscribe {
    const collectionRef = collection(db, this.collectionName)
    const q = query(collectionRef, orderBy('createdAt', 'desc'), ...constraints)

    return onSnapshot(
      q,
      (snapshot) => {
        const data = snapshot.docs.map((doc) =>
          this.convertDocumentData(doc.id, doc.data())
        )
        onData(data)
      },
      (error) => {
        console.error(`Error subscribing to ${this.collectionName}:`, error)
        onError(error)
      }
    )
  }

  /**
   * Create a new document
   */
  async create(data: CreateEntityData<T>): Promise<RepositoryResult<T>> {
    try {
      const collectionRef = collection(db, this.collectionName)
      const docData = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      const docRef = await addDoc(collectionRef, docData)
      
      // Return the created entity with the generated ID
      const createdEntity = {
        id: docRef.id,
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as T

      return {
        success: true,
        data: createdEntity,
      }
    } catch (error) {
      console.error(`Error creating ${this.collectionName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  /**
   * Update an existing document
   */
  async update(id: string, data: UpdateEntityData<T>): Promise<RepositoryResult<T>> {
    try {
      const docRef = doc(db, this.collectionName, id)
      const updateData = {
        ...data,
        updatedAt: serverTimestamp(),
      }

      await updateDoc(docRef, updateData)

      // Return the updated entity (note: we don't have the full entity here)
      const updatedEntity = {
        id,
        ...data,
        updatedAt: new Date(),
      } as T

      return {
        success: true,
        data: updatedEntity,
      }
    } catch (error) {
      console.error(`Error updating ${this.collectionName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  /**
   * Delete a document
   */
  async delete(id: string): Promise<RepositoryResult<void>> {
    try {
      const docRef = doc(db, this.collectionName, id)
      await deleteDoc(docRef)

      return {
        success: true,
      }
    } catch (error) {
      console.error(`Error deleting ${this.collectionName}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }
}
