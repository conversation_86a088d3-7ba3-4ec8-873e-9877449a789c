import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/container/card"
import { Mail, Phone } from "lucide-react"
import type { Client } from "@/types/client"

interface ClientCardProps {
  client: Client
}

export function ClientCard({ client }: ClientCardProps) {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">{client.name}</CardTitle>
        <CardDescription>Status: {client.status}</CardDescription>
      </CardHeader>
      <CardContent className="pt-0 text-sm space-y-1">
        {client.contactEmail && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Mail className="size-4" />
            <span>{client.contactEmail}</span>
          </div>
        )}
        {client.contactPhone && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Phone className="size-4" />
            <span>{client.contactPhone}</span>
          </div>
        )}
      </Card<PERSON>ontent>
    </Card>
  )
}
