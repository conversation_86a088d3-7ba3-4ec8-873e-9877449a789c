import { useState, useEffect, useCallback } from 'react'
import { clientService } from '@/services'
import type { Client, CreateClientData, UpdateClientData, RepositoryResult } from '@/types'

interface UseClientsState {
  clients: Client[]
  loading: boolean
  error: string | null
}

interface UseClientsActions {
  createClient: (data: CreateClientData) => Promise<RepositoryResult<Client>>
  updateClient: (id: string, data: UpdateClientData) => Promise<RepositoryResult<Client>>
  deleteClient: (id: string) => Promise<RepositoryResult<void>>
  refetch: () => void
  clearError: () => void
}

interface UseClientsReturn extends UseClientsState, UseClientsActions {
  stats: {
    total: number
    active: number
    prospects: number
    inactive: number
  }
}

/**
 * Custom hook for managing clients with real-time subscriptions
 * Provides CRUD operations and loading/error states
 */
export function useClients(): UseClientsReturn {
  const [state, setState] = useState<UseClientsState>({
    clients: [],
    loading: true,
    error: null,
  })

  // Set up real-time subscription
  useEffect(() => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    const unsubscribe = clientService.subscribeToClients(
      (clients) => {
        setState({
          clients,
          loading: false,
          error: null,
        })
      },
      (error) => {
        setState(prev => ({
          ...prev,
          loading: false,
          error: error.message || 'Failed to load clients',
        }))
      }
    )

    // Cleanup subscription on unmount
    return () => unsubscribe()
  }, [])

  // CRUD operations
  const createClient = useCallback(async (data: CreateClientData): Promise<RepositoryResult<Client>> => {
    const result = await clientService.createClient(data)
    
    if (!result.success) {
      setState(prev => ({ ...prev, error: result.error || 'Failed to create client' }))
    }
    
    return result
  }, [])

  const updateClient = useCallback(async (id: string, data: UpdateClientData): Promise<RepositoryResult<Client>> => {
    const result = await clientService.updateClient(id, data)
    
    if (!result.success) {
      setState(prev => ({ ...prev, error: result.error || 'Failed to update client' }))
    }
    
    return result
  }, [])

  const deleteClient = useCallback(async (id: string): Promise<RepositoryResult<void>> => {
    const result = await clientService.deleteClient(id)
    
    if (!result.success) {
      setState(prev => ({ ...prev, error: result.error || 'Failed to delete client' }))
    }
    
    return result
  }, [])

  const refetch = useCallback(() => {
    // The real-time subscription will automatically refetch
    // This is here for consistency with typical hook patterns
    setState(prev => ({ ...prev, loading: true, error: null }))
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Calculate stats
  const stats = clientService.getClientStats(state.clients)

  return {
    ...state,
    stats,
    createClient,
    updateClient,
    deleteClient,
    refetch,
    clearError,
  }
}
