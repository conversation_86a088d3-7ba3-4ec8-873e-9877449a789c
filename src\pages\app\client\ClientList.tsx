import { Layout } from "@/components/ui/layout"
import { Card } from "@/components/ui/container/card"
import { Button } from "@/components/ui/basic/button"
import { Plus } from "lucide-react"
import { useNavigate } from "react-router-dom"
import { ClientCard } from "@/components/pages/clientList"
import { useClients } from "@/hooks"

function ClientList() {
  const navigate = useNavigate()
  const { clients, loading, error, stats, clearError } = useClients()

  const goToAddClient = () => navigate("/clients/new")

  return (
    <Layout title="Clients">
      <section className="max-w-7xl mx-auto px-4 py-6">
        {/* Error display */}
        {error && (
          <div className="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={clearError}
              className="mt-2"
            >
              Dismiss
            </Button>
          </div>
        )}

        {/* Stats display */}
        {!loading && clients.length > 0 && (
          <div className="mb-6 grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
            <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
            <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.prospects}</div>
              <div className="text-xs text-muted-foreground">Prospects</div>
            </div>
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-950/20 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{stats.inactive}</div>
              <div className="text-xs text-muted-foreground">Inactive</div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 rounded-lg bg-muted/50 animate-pulse" />
            ))}
          </div>
        ) : clients.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {clients.map((c) => (
              <ClientCard key={c.id} client={c} />
            ))}

            <Card
              className="flex items-center justify-center border-dashed cursor-pointer hover:bg-accent/40 transition-colors"
              onClick={goToAddClient}
              role="button"
              aria-label="Add client"
            >
              <div className="flex flex-col items-center gap-2 p-8">
                <Button variant="outline" size="lg" className="gap-2" onClick={goToAddClient}>
                  <Plus />
                  Add client
                </Button>
                <p className="text-xs text-muted-foreground">Create a new client</p>
              </div>
            </Card>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center rounded-lg border border-dashed py-14">
            <p className="text-sm text-muted-foreground mb-4">No clients yet.</p>
            <Button onClick={goToAddClient} className="gap-2">
              <Plus />
              Add your first client
            </Button>
          </div>
        )}
      </section>
    </Layout>
  )
}

export default ClientList

