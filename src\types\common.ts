// Common types for the application

export interface BaseEntity {
  id: string
  createdAt: Date
  updatedAt: Date
}

export interface CreateEntityData<T> extends Omit<T, keyof BaseEntity> {}

export interface UpdateEntityData<T> extends Partial<Omit<T, keyof BaseEntity>> {}

// Repository operation results
export interface RepositoryResult<T> {
  success: boolean
  data?: T
  error?: string
}

export interface RepositoryListResult<T> {
  success: boolean
  data?: T[]
  error?: string
}

// Subscription callback types
export type SubscriptionCallback<T> = (data: T[]) => void
export type SubscriptionErrorCallback = (error: Error) => void

// Firestore timestamp conversion utility type
export interface FirestoreTimestamp {
  seconds: number
  nanoseconds: number
}
